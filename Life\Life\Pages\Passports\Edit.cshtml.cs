using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.Passports
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int PassportId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Passport Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Passport Number is required")]
        [StringLength(10, ErrorMessage = "Passport Number cannot exceed 10 characters")]
        [DisplayName("Passport Number")]
        public string PassportNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        [BindProperty]
        [DisplayName("Cost")]
        public decimal? Cost { get; set; }

        [BindProperty]
        [StringLength(20, ErrorMessage = "Size cannot exceed 20 characters")]
        [DisplayName("Size")]
        public string? Size { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var passport = await _context.Passports
                .Where(p => p.Id == id && p.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (passport == null)
            {
                return NotFound();
            }

            PassportId = passport.Id;
            Name = passport.Name;
            PassportNumber = passport.PassportNumber;
            StartDate = passport.StartDate;
            ExpiryDate = passport.ExpiryDate;
            Cost = passport.Cost;
            Size = passport.Size;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
                return Page();
            }

            // Get the existing passport
            var existingPassport = await _context.Passports
                .Where(p => p.Id == PassportId && p.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (existingPassport == null)
            {
                return NotFound();
            }

            // Store the original expiry date to check if it changed
            var originalExpiryDate = existingPassport.ExpiryDate;

            // Update the passport fields
            existingPassport.Name = Name;
            existingPassport.PassportNumber = PassportNumber;
            existingPassport.StartDate = StartDate;
            existingPassport.ExpiryDate = ExpiryDate;
            existingPassport.Cost = Cost;
            existingPassport.Size = Size;
            existingPassport.UpdatedAt = DateTime.UtcNow;

            // If the expiry date changed, update the linked reminder
            if (originalExpiryDate != ExpiryDate)
            {
                var linkedReminder = await _context.Reminders
                    .Where(r => r.PassportId == PassportId && r.IsAutomatic == true)
                    .FirstOrDefaultAsync();

                if (linkedReminder != null)
                {
                    linkedReminder.ReminderDate = ExpiryDate;
                    linkedReminder.Name = $"{Name} - Passport Expiry";
                    linkedReminder.UpdatedAt = DateTime.UtcNow;
                }
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!PassportExists(PassportId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "Passport updated successfully";

            return RedirectToPage("./Index");
        }

        private bool PassportExists(int id)
        {
            return _context.Passports.Any(e => e.Id == id);
        }
    }
}
