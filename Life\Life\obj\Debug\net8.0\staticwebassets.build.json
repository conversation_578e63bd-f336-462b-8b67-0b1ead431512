{"Version": 1, "Hash": "Gm09yiK+F/y9JYUISdqL1RMh2Io5H1rn9dz9ITuOpdY=", "Source": "Life", "BasePath": "_content/Life", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Life\\wwwroot", "Source": "Life", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Life.styles.css", "SourceId": "Life", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/Life", "RelativePath": "Life#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "majr3<PERSON><PERSON><PERSON>v", "Integrity": "kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Life.styles.css", "FileLength": 1122, "LastWriteTime": "2025-07-16T10:29:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Life.bundle.scp.css", "SourceId": "Life", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Life", "RelativePath": "Life#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "majr3<PERSON><PERSON><PERSON>v", "Integrity": "kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Life.bundle.scp.css", "FileLength": 1122, "LastWriteTime": "2025-07-16T10:29:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\bootstrapoverrides.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/bootstrapoverrides#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bmemeer8bj", "Integrity": "g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrapoverrides.css", "FileLength": 4314, "LastWriteTime": "2025-07-18T11:35:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\dashboard.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v17xt12bv5", "Integrity": "DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 4110, "LastWriteTime": "2025-07-16T14:26:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\header.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/header#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "30qyo1la85", "Integrity": "3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\header.css", "FileLength": 2261, "LastWriteTime": "2025-07-16T14:22:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\main.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/main#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "93hh85iudv", "Integrity": "O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\main.css", "FileLength": 351, "LastWriteTime": "2025-07-16T14:23:26+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\sidebar.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/sidebar#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "urz00uwuq2", "Integrity": "Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\sidebar.css", "FileLength": 3874, "LastWriteTime": "2025-07-16T14:24:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\site.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qrula2m4zv", "Integrity": "BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1465, "LastWriteTime": "2025-07-21T14:35:55+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\js\\site.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w5okk3xrsw", "Integrity": "rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 4419, "LastWriteTime": "2025-07-16T14:31:58+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2w652sptew", "Integrity": "IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 237950, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yk6841fnnu", "Integrity": "rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 608300, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "00o7me0wi0", "Integrity": "wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 194901, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tlr56zwpn9", "Integrity": "/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 522639, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9065ftxq2z", "Integrity": "V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 237528, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "slkxxxvi35", "Integrity": "cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 608144, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tf8d8chiq4", "Integrity": "+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 195007, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lbdsg3nnm3", "Integrity": "j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 767483, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4qewknlhhe", "Integrity": "dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 71861, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lni73340tj", "Integrity": "z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 210357, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dldhcrg7tj", "Integrity": "7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 53265, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7mduziv6u0", "Integrity": "kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 131395, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k77ja6ojiw", "Integrity": "QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 71935, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o2603f55wz", "Integrity": "wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 210361, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1h967pd9y7", "Integrity": "iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 53340, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c9vlz3rxj1", "Integrity": "gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 131472, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aa7xbl0rhm", "Integrity": "3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 7965, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l3dvo8xkxk", "Integrity": "72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 110875, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f60t7moryz", "Integrity": "J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 6490, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2hu6j6r8fj", "Integrity": "xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 40331, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0ewczrgofc", "Integrity": "CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 7958, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1g6wsxogud", "Integrity": "X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 110888, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gyj16cx8tl", "Integrity": "SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 6562, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6k0x1vcin3", "Integrity": "Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 48693, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "avhg9diubf", "Integrity": "dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 76347, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nmg369ex7u", "Integrity": "7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 212450, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3lukdf9ezp", "Integrity": "KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 58266, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hlpl0prczm", "Integrity": "1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 131956, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p13uj7zzzw", "Integrity": "EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 76214, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rat98tutyt", "Integrity": "k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 212393, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cwuaox3pd4", "Integrity": "7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 58194, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j526q8vwd3", "Integrity": "Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 131791, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kpvfgawzjd", "Integrity": "SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207989, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qm38hbak0o", "Integrity": "nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 451770, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pjhbf5ty2q", "Integrity": "lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80420, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "llx8tuhx2q", "Integrity": "GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 333078, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "axg36zpfrh", "Integrity": "bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 136215, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o6r5icn62k", "Integrity": "IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 308207, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "uotu6b0c8w", "Integrity": "MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73978, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o1d4puk7gs", "Integrity": "DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 221179, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ib7told66d", "Integrity": "TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145543, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s9kdte20bp", "Integrity": "4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 309348, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xtzwxpr1me", "Integrity": "m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60404, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lxnhmosdkt", "Integrity": "lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 216913, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-16T10:10:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\logo.png", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47qgg4w1ko", "Integrity": "oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo.png", "FileLength": 12987, "LastWriteTime": "2025-06-30T19:37:22+00:00"}], "Endpoints": [{"Route": "css/bootstrapoverrides.bmemeer8bj.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\bootstrapoverrides.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 11:35:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bmemeer8bj"}, {"Name": "label", "Value": "css/bootstrapoverrides.css"}, {"Name": "integrity", "Value": "sha256-g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q="}]}, {"Route": "css/bootstrapoverrides.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\bootstrapoverrides.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 11:35:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q="}]}, {"Route": "css/dashboard.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4110"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:26:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q="}]}, {"Route": "css/dashboard.v17xt12bv5.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4110"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:26:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v17xt12bv5"}, {"Name": "label", "Value": "css/dashboard.css"}, {"Name": "integrity", "Value": "sha256-DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q="}]}, {"Route": "css/header.30qyo1la85.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2261"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:22:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "30qyo1la85"}, {"Name": "label", "Value": "css/header.css"}, {"Name": "integrity", "Value": "sha256-3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY="}]}, {"Route": "css/header.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2261"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:22:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY="}]}, {"Route": "css/main.93hh85iudv.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:23:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "93hh85iudv"}, {"Name": "label", "Value": "css/main.css"}, {"Name": "integrity", "Value": "sha256-O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4="}]}, {"Route": "css/main.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:23:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4="}]}, {"Route": "css/sidebar.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\sidebar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3874"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:24:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE="}]}, {"Route": "css/sidebar.urz00uwuq2.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\sidebar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3874"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:24:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "urz00uwuq2"}, {"Name": "label", "Value": "css/sidebar.css"}, {"Name": "integrity", "Value": "sha256-Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE="}]}, {"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1465"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:35:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc="}]}, {"Route": "css/site.qrula2m4zv.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1465"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:35:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qrula2m4zv"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc="}]}, {"Route": "js/site.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:31:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA="}]}, {"Route": "js/site.w5okk3xrsw.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:31:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w5okk3xrsw"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.2w652sptew.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "237950"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2w652sptew"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "237950"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "608300"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.yk6841fnnu.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "608300"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yk6841fnnu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.00o7me0wi0.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "194901"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00o7me0wi0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "194901"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "522639"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.tlr56zwpn9.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "522639"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tlr56zwpn9"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.9065ftxq2z.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "237528"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9065ftxq2z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "237528"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "608144"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.slkxxxvi35.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "608144"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "slkxxxvi35"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.lbdsg3nnm3.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "767483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbdsg3nnm3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "767483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.tf8d8chiq4.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tf8d8chiq4"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.4qewknlhhe.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71861"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4qewknlhhe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71861"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.lni73340tj.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "210357"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lni73340tj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "210357"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53265"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.7mduziv6u0.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7mduziv6u0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.dldhcrg7tj.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53265"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dldhcrg7tj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71935"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "210361"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.o2603f55wz.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "210361"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2603f55wz"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.k77ja6ojiw.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71935"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k77ja6ojiw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.1h967pd9y7.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53340"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1h967pd9y7"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53340"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.c9vlz3rxj1.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131472"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c9vlz3rxj1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131472"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.aa7xbl0rhm.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aa7xbl0rhm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.l3dvo8xkxk.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l3dvo8xkxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6490"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.2hu6j6r8fj.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40331"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2hu6j6r8fj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40331"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.f60t7moryz.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6490"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f60t7moryz"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.0ewczrgofc.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ewczrgofc"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.1g6wsxogud.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110888"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1g6wsxogud"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110888"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.6k0x1vcin3.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48693"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6k0x1vcin3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48693"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.gyj16cx8tl.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyj16cx8tl"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.avhg9diubf.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "avhg9diubf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "212450"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.nmg369ex7u.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "212450"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nmg369ex7u"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.3lukdf9ezp.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3lukdf9ezp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.hlpl0prczm.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hlpl0prczm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76214"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "212393"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.rat98tutyt.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "212393"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rat98tutyt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.j526q8vwd3.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131791"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j526q8vwd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "131791"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.cwuaox3pd4.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuaox3pd4"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.p13uj7zzzw.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76214"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p13uj7zzzw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "451770"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.qm38hbak0o.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "451770"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qm38hbak0o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.kpvfgawzjd.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kpvfgawzjd"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.llx8tuhx2q.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "333078"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "llx8tuhx2q"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "333078"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.pjhbf5ty2q.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjhbf5ty2q"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.axg36zpfrh.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "axg36zpfrh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "308207"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.o6r5icn62k.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "308207"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o6r5icn62k"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73978"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "221179"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.o1d4puk7gs.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "221179"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1d4puk7gs"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.uotu6b0c8w.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73978"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uotu6b0c8w"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.ib7told66d.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145543"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ib7told66d"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145543"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "309348"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.s9kdte20bp.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "309348"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s9kdte20bp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60404"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.lxnhmosdkt.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216913"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lxnhmosdkt"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216913"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.xtzwxpr1me.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60404"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtzwxpr1me"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/jquery/dist/jquery.fwhahm2icz.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fwhahm2icz"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.min.5pze98is44.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5pze98is44"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "lib/jquery/dist/jquery.min.dd6z7egasc.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dd6z7egasc"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.ay5nd8zt9x.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5nd8zt9x"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.9oaff4kq20.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9oaff4kq20"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "Life.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Life.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}]}, {"Route": "Life.majr3oelrv.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Life.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "majr3<PERSON><PERSON><PERSON>v"}, {"Name": "label", "Value": "Life.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}]}, {"Route": "Life.majr3oelrv.styles.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Life.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "majr3<PERSON><PERSON><PERSON>v"}, {"Name": "label", "Value": "Life.styles.css"}, {"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}]}, {"Route": "Life.styles.css", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Life.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}]}, {"Route": "logo.47qgg4w1ko.png", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 19:37:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47qgg4w1ko"}, {"Name": "label", "Value": "logo.png"}, {"Name": "integrity", "Value": "sha256-oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc="}]}, {"Route": "logo.png", "AssetFile": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 19:37:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc="}]}]}