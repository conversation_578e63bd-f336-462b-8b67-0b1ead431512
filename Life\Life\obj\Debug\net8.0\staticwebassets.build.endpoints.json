{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Life.majr3oelrv.styles.css", "AssetFile": "Life.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "majr3<PERSON><PERSON><PERSON>v"}, {"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}, {"Name": "label", "Value": "Life.styles.css"}]}, {"Route": "Life.styles.css", "AssetFile": "Life.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:29:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgILruXZNUql6Ha6DRFKDD8A86tmcOFo8acwuTW2KRc="}]}, {"Route": "css/bootstrapoverrides.bmemeer8bj.css", "AssetFile": "css/bootstrapoverrides.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 11:35:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bmemeer8bj"}, {"Name": "integrity", "Value": "sha256-g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q="}, {"Name": "label", "Value": "css/bootstrapoverrides.css"}]}, {"Route": "css/bootstrapoverrides.css", "AssetFile": "css/bootstrapoverrides.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 11:35:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q="}]}, {"Route": "css/dashboard.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4110"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:26:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q="}]}, {"Route": "css/dashboard.v17xt12bv5.css", "AssetFile": "css/dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4110"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:26:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v17xt12bv5"}, {"Name": "integrity", "Value": "sha256-DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q="}, {"Name": "label", "Value": "css/dashboard.css"}]}, {"Route": "css/header.30qyo1la85.css", "AssetFile": "css/header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2261"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:22:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "30qyo1la85"}, {"Name": "integrity", "Value": "sha256-3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY="}, {"Name": "label", "Value": "css/header.css"}]}, {"Route": "css/header.css", "AssetFile": "css/header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2261"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:22:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY="}]}, {"Route": "css/main.93hh85iudv.css", "AssetFile": "css/main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:23:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "93hh85iudv"}, {"Name": "integrity", "Value": "sha256-O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4="}, {"Name": "label", "Value": "css/main.css"}]}, {"Route": "css/main.css", "AssetFile": "css/main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:23:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4="}]}, {"Route": "css/sidebar.css", "AssetFile": "css/sidebar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3874"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:24:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE="}]}, {"Route": "css/sidebar.urz00uwuq2.css", "AssetFile": "css/sidebar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3874"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:24:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "urz00uwuq2"}, {"Name": "integrity", "Value": "sha256-Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE="}, {"Name": "label", "Value": "css/sidebar.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1465"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:35:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc="}]}, {"Route": "css/site.qrula2m4zv.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1465"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 14:35:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qrula2m4zv"}, {"Name": "integrity", "Value": "sha256-BoFYybUB0HEmGncKMmLna+iQNOZrkpDOQwpz5r1LUSc="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:31:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA="}]}, {"Route": "js/site.w5okk3xrsw.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4419"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 14:31:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w5okk3xrsw"}, {"Name": "integrity", "Value": "sha256-rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.4qewknlhhe.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71861"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4qewknlhhe"}, {"Name": "integrity", "Value": "sha256-dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71861"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.lni73340tj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "210357"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lni73340tj"}, {"Name": "integrity", "Value": "sha256-z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "210357"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53265"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.7mduziv6u0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7mduziv6u0"}, {"Name": "integrity", "Value": "sha256-kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.dldhcrg7tj.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53265"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dldhcrg7tj"}, {"Name": "integrity", "Value": "sha256-7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71935"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "210361"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.o2603f55wz.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "210361"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2603f55wz"}, {"Name": "integrity", "Value": "sha256-wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.k77ja6ojiw.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71935"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k77ja6ojiw"}, {"Name": "integrity", "Value": "sha256-QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.1h967pd9y7.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53340"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1h967pd9y7"}, {"Name": "integrity", "Value": "sha256-iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53340"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.c9vlz3rxj1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131472"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c9vlz3rxj1"}, {"Name": "integrity", "Value": "sha256-gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131472"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.aa7xbl0rhm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aa7xbl0rhm"}, {"Name": "integrity", "Value": "sha256-3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.l3dvo8xkxk.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "110875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l3dvo8xkxk"}, {"Name": "integrity", "Value": "sha256-72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "110875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6490"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.2hu6j6r8fj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "40331"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2hu6j6r8fj"}, {"Name": "integrity", "Value": "sha256-xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "40331"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.f60t7moryz.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6490"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f60t7moryz"}, {"Name": "integrity", "Value": "sha256-J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.0ewczrgofc.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ewczrgofc"}, {"Name": "integrity", "Value": "sha256-CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.1g6wsxogud.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "110888"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1g6wsxogud"}, {"Name": "integrity", "Value": "sha256-X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "110888"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.6k0x1vcin3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48693"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6k0x1vcin3"}, {"Name": "integrity", "Value": "sha256-Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48693"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.gyj16cx8tl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyj16cx8tl"}, {"Name": "integrity", "Value": "sha256-SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.avhg9diubf.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "76347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "avhg9diubf"}, {"Name": "integrity", "Value": "sha256-dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "76347"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "212450"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.nmg369ex7u.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "212450"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nmg369ex7u"}, {"Name": "integrity", "Value": "sha256-7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.3lukdf9ezp.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "58266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3lukdf9ezp"}, {"Name": "integrity", "Value": "sha256-KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "58266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.hlpl0prczm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hlpl0prczm"}, {"Name": "integrity", "Value": "sha256-1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "76214"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "212393"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.rat98tutyt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "212393"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rat98tutyt"}, {"Name": "integrity", "Value": "sha256-k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "58194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.j526q8vwd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131791"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j526q8vwd3"}, {"Name": "integrity", "Value": "sha256-Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131791"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.cwuaox3pd4.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "58194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuaox3pd4"}, {"Name": "integrity", "Value": "sha256-7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.p13uj7zzzw.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "76214"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p13uj7zzzw"}, {"Name": "integrity", "Value": "sha256-EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.2w652sptew.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "237950"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2w652sptew"}, {"Name": "integrity", "Value": "sha256-IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "237950"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "608300"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.yk6841fnnu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "608300"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yk6841fnnu"}, {"Name": "integrity", "Value": "sha256-rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.00o7me0wi0.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "194901"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00o7me0wi0"}, {"Name": "integrity", "Value": "sha256-wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "194901"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "522639"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.tlr56zwpn9.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "522639"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tlr56zwpn9"}, {"Name": "integrity", "Value": "sha256-/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.9065ftxq2z.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "237528"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9065ftxq2z"}, {"Name": "integrity", "Value": "sha256-V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "237528"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "608144"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.slkxxxvi35.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "608144"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "slkxxxvi35"}, {"Name": "integrity", "Value": "sha256-cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "195007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.lbdsg3nnm3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "767483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbdsg3nnm3"}, {"Name": "integrity", "Value": "sha256-j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "767483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.tf8d8chiq4.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "195007"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tf8d8chiq4"}, {"Name": "integrity", "Value": "sha256-+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "207989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "451770"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.qm38hbak0o.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "451770"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qm38hbak0o"}, {"Name": "integrity", "Value": "sha256-nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.kpvfgawzjd.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "207989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kpvfgawzjd"}, {"Name": "integrity", "Value": "sha256-SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "80420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.llx8tuhx2q.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "333078"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "llx8tuhx2q"}, {"Name": "integrity", "Value": "sha256-GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "333078"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.pjhbf5ty2q.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "80420"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjhbf5ty2q"}, {"Name": "integrity", "Value": "sha256-lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.axg36zpfrh.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "136215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "axg36zpfrh"}, {"Name": "integrity", "Value": "sha256-bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "136215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "308207"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.o6r5icn62k.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "308207"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o6r5icn62k"}, {"Name": "integrity", "Value": "sha256-IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73978"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "221179"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.o1d4puk7gs.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "221179"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1d4puk7gs"}, {"Name": "integrity", "Value": "sha256-DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.uotu6b0c8w.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73978"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uotu6b0c8w"}, {"Name": "integrity", "Value": "sha256-MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.ib7told66d.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "145543"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ib7told66d"}, {"Name": "integrity", "Value": "sha256-TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "145543"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "309348"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.s9kdte20bp.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "309348"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s9kdte20bp"}, {"Name": "integrity", "Value": "sha256-4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60404"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.lxnhmosdkt.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "216913"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lxnhmosdkt"}, {"Name": "integrity", "Value": "sha256-lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "216913"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.xtzwxpr1me.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "60404"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=\""}, {"Name": "Last-Modified", "Value": "Thu, 09 Mar 2023 09:11:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtzwxpr1me"}, {"Name": "integrity", "Value": "sha256-m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.ay5nd8zt9x.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5nd8zt9x"}, {"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.9oaff4kq20.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9oaff4kq20"}, {"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/dist/jquery.fwhahm2icz.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fwhahm2icz"}, {"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.min.5pze98is44.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5pze98is44"}, {"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}]}, {"Route": "lib/jquery/dist/jquery.min.dd6z7egasc.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dd6z7egasc"}, {"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 10:10:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "logo.47qgg4w1ko.png", "AssetFile": "logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 19:37:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47qgg4w1ko"}, {"Name": "integrity", "Value": "sha256-oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc="}, {"Name": "label", "Value": "logo.png"}]}, {"Route": "logo.png", "AssetFile": "logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 19:37:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc="}]}]}