@page
@model Life.Pages.Reminders.DeleteModel
@{
    ViewData["Title"] = "Delete Reminder";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Delete Reminder</h4>
                </div>
                <div class="card-body">
                    <h5 class="mt-3">Confirm you want to delete this reminder;</h5>
                    <h6>@Model.Name</h6>
                    <form method="post" class="row g-3 mt-1">
						<input type="hidden" asp-for="ReminderId" />
                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-danger">Confirm Deletion</button>
                                <a asp-page="./Index" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
