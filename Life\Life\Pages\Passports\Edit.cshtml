@page
@model Life.Pages.Passports.EditModel
@{
    ViewData["Title"] = "Edit Passport";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Edit Passport</h2>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">
						<input type="hidden" asp-for="PassportId" />

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Name" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="PassportNumber" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="PassportNumber" class="form-control" />
                                <span asp-validation-for="PassportNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="StartDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="StartDate" class="form-control" type="date" />
                                <span asp-validation-for="StartDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="ExpiryDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="ExpiryDate" class="form-control" type="date" />
                                <span asp-validation-for="ExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Cost" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Cost" class="form-control" />
                                <span asp-validation-for="Cost" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Size" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <select asp-for="Size" class="form-control">
                                    <option value="Not Set">Optional</option>
                                    <option value="Standard">Standard</option>
                                    <option value="Large">Large</option>
                                </select>
                                <span asp-validation-for="Size" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
