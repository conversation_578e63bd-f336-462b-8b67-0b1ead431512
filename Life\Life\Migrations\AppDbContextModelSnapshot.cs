﻿// <auto-generated />
using System;
using Life.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Life.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.18")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Life.Models.Address", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HouseFlatNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateOnly?>("MoveInDate")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("MoveOutDate")
                        .HasColumnType("date");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Postcode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateOnly?>("PurchaseDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("PurchasePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("SoldDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("SoldPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("StreetLineOne")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetLineTwo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("Life.Models.AddressInsurancePolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRenewal")
                        .HasColumnType("bit");

                    b.Property<decimal?>("BuildingsCover")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BuildingsExcess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClaimsPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("ContentsCover")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ContentsExcess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("PolicyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PolicyNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("AddressInsurancePolicies");
                });

            modelBuilder.Entity("Life.Models.CreditCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("CreditLimit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<DateOnly?>("PromotionalPeriodEndDate")
                        .HasColumnType("date");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("CreditCards");
                });

            modelBuilder.Entity("Life.Models.CreditCardAprHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CreditCardId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("CreditCardId");

                    b.HasIndex("UserId");

                    b.ToTable("CreditCardAprHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.CurrentAccount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("DateOpened")
                        .HasColumnType("date");

                    b.Property<decimal?>("InterestApr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinimumMonthlyFunding")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MonthlyFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("OverdraftApr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OverdraftLimit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("CurrentAccounts");
                });

            modelBuilder.Entity("Life.Models.CurrentAccountAprHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CurrentAccountId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("CurrentAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("CurrentAccountAprHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.DebitCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CurrentAccountId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("CurrentAccountId")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("DebitCards");
                });

            modelBuilder.Entity("Life.Models.Dentist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Postcode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("StreetLineOne")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetLineTwo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("Website")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Dentists");
                });

            modelBuilder.Entity("Life.Models.DirectDebit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CurrentAccountId")
                        .HasColumnType("int");

                    b.Property<int?>("GadgetInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("GeneralItemId")
                        .HasColumnType("int");

                    b.Property<int?>("LifeInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("LoanId")
                        .HasColumnType("int");

                    b.Property<int?>("MortgageId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("PaymentFrequency")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PaymentMonth")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("TenancyId")
                        .HasColumnType("int");

                    b.Property<int?>("TravelInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int?>("UtilityBillId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleFinanceAgreementId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleTaxId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddressInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[AddressInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("CurrentAccountId");

                    b.HasIndex("GadgetInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[GadgetInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("GeneralItemId")
                        .IsUnique()
                        .HasFilter("[GeneralItemId] IS NOT NULL");

                    b.HasIndex("LifeInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[LifeInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("LoanId")
                        .IsUnique()
                        .HasFilter("[LoanId] IS NOT NULL");

                    b.HasIndex("MortgageId")
                        .IsUnique()
                        .HasFilter("[MortgageId] IS NOT NULL");

                    b.HasIndex("TenancyId")
                        .IsUnique()
                        .HasFilter("[TenancyId] IS NOT NULL");

                    b.HasIndex("TravelInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[TravelInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.HasIndex("UtilityBillId")
                        .IsUnique()
                        .HasFilter("[UtilityBillId] IS NOT NULL");

                    b.HasIndex("VehicleFinanceAgreementId")
                        .IsUnique()
                        .HasFilter("[VehicleFinanceAgreementId] IS NOT NULL");

                    b.HasIndex("VehicleInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[VehicleInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("VehicleTaxId")
                        .IsUnique()
                        .HasFilter("[VehicleTaxId] IS NOT NULL");

                    b.ToTable("DirectDebits");
                });

            modelBuilder.Entity("Life.Models.DrivingLicence", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("LicenceNumber")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateOnly>("PhotocardExpiryDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("PhotocardStartDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("DrivingLicences");
                });

            modelBuilder.Entity("Life.Models.EyeTest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("LeftAXIS")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LeftCYL")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LeftSPH")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("NextTestDate")
                        .HasColumnType("date");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("RightAXIS")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("RightCYL")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("RightSPH")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("TestDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("EyeTests");
                });

            modelBuilder.Entity("Life.Models.GadgetInsurancePolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRenewal")
                        .HasColumnType("bit");

                    b.Property<string>("ClaimsPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PolicyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PolicyNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("GadgetInsurancePolicies");
                });

            modelBuilder.Entity("Life.Models.GeneralItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Details")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateOnly?>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("GeneralItems");
                });

            modelBuilder.Entity("Life.Models.GlobalHealthInsuranceCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CardIdNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PersonalIdNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("GlobalHealthInsuranceCards");
                });

            modelBuilder.Entity("Life.Models.GpPractice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Postcode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("StreetLineOne")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetLineTwo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("Website")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("GpPractices");
                });

            modelBuilder.Entity("Life.Models.LifeInsurancePolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<decimal>("InsuranceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MonthlyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PolicyNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("LifeInsurancePolicies");
                });

            modelBuilder.Entity("Life.Models.Loan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("ExpectedEndDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("LoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LoanTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("RemainingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("Settled")
                        .HasColumnType("bit");

                    b.Property<DateOnly?>("SettledDate")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("TotalRepayableAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Loans");
                });

            modelBuilder.Entity("Life.Models.Mortgage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ArrangementFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BookingFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("EarlyRepaymentCharge")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("EndDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("FixedTermApr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("FixedTermEndDate")
                        .HasColumnType("date");

                    b.Property<int?>("FixedTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("RemainingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("RemittanceFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("StandardVariableRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<int?>("TotalTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<decimal?>("ValuationFee")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Mortgages");
                });

            modelBuilder.Entity("Life.Models.Passport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("PassportNumber")
                        .HasColumnType("int");

                    b.Property<string>("Size")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Passports");
                });

            modelBuilder.Entity("Life.Models.Pension", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<decimal?>("AnnualChargePercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ContributionFeePercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("DateOpened")
                        .HasColumnType("date");

                    b.Property<decimal?>("MonthlyContributionAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MonthlyEmployerContributionAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Pensions");
                });

            modelBuilder.Entity("Life.Models.PensionBalanceHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<int>("PensionId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("PensionId");

                    b.HasIndex("UserId");

                    b.ToTable("PensionBalanceHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.RecurringPayment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreditCardId")
                        .HasColumnType("int");

                    b.Property<int?>("DebitCardId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("NextPaymentDate")
                        .HasColumnType("date");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("PaymentFrequency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PaymentMonth")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("CreditCardId");

                    b.HasIndex("DebitCardId");

                    b.HasIndex("UserId");

                    b.ToTable("RecurringPayments");
                });

            modelBuilder.Entity("Life.Models.Reminder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreditCardId")
                        .HasColumnType("int");

                    b.Property<int?>("DebitCardId")
                        .HasColumnType("int");

                    b.Property<int?>("DrivingLicenceId")
                        .HasColumnType("int");

                    b.Property<int?>("EyeTestId")
                        .HasColumnType("int");

                    b.Property<int?>("GadgetInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("GeneralItemId")
                        .HasColumnType("int");

                    b.Property<int?>("GlobalHealthInsuranceCardId")
                        .HasColumnType("int");

                    b.Property<bool>("IsAutomatic")
                        .HasColumnType("bit");

                    b.Property<int?>("LifeInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("LoanId")
                        .HasColumnType("int");

                    b.Property<int?>("MortgageId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<int?>("PassportId")
                        .HasColumnType("int");

                    b.Property<int?>("RecurringPaymentId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("ReminderDate")
                        .HasColumnType("date");

                    b.Property<int?>("SavingsAccountId")
                        .HasColumnType("int");

                    b.Property<int?>("SavingsAccountPotId")
                        .HasColumnType("int");

                    b.Property<int?>("TenancyId")
                        .HasColumnType("int");

                    b.Property<int?>("TravelInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int?>("UtilityBillId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleBreakdownPolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleFinanceAgreementId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleMotId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleServicePlanId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleTaxId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleWarrantyId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddressInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[AddressInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("CreditCardId");

                    b.HasIndex("DebitCardId")
                        .IsUnique()
                        .HasFilter("[DebitCardId] IS NOT NULL");

                    b.HasIndex("DrivingLicenceId");

                    b.HasIndex("EyeTestId")
                        .IsUnique()
                        .HasFilter("[EyeTestId] IS NOT NULL");

                    b.HasIndex("GadgetInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[GadgetInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("GeneralItemId")
                        .IsUnique()
                        .HasFilter("[GeneralItemId] IS NOT NULL");

                    b.HasIndex("GlobalHealthInsuranceCardId")
                        .IsUnique()
                        .HasFilter("[GlobalHealthInsuranceCardId] IS NOT NULL");

                    b.HasIndex("LifeInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[LifeInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("LoanId")
                        .IsUnique()
                        .HasFilter("[LoanId] IS NOT NULL");

                    b.HasIndex("MortgageId");

                    b.HasIndex("PassportId")
                        .IsUnique()
                        .HasFilter("[PassportId] IS NOT NULL");

                    b.HasIndex("RecurringPaymentId")
                        .IsUnique()
                        .HasFilter("[RecurringPaymentId] IS NOT NULL");

                    b.HasIndex("SavingsAccountId")
                        .IsUnique()
                        .HasFilter("[SavingsAccountId] IS NOT NULL");

                    b.HasIndex("SavingsAccountPotId")
                        .IsUnique()
                        .HasFilter("[SavingsAccountPotId] IS NOT NULL");

                    b.HasIndex("TenancyId")
                        .IsUnique()
                        .HasFilter("[TenancyId] IS NOT NULL");

                    b.HasIndex("TravelInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[TravelInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.HasIndex("UtilityBillId");

                    b.HasIndex("VehicleBreakdownPolicyId")
                        .IsUnique()
                        .HasFilter("[VehicleBreakdownPolicyId] IS NOT NULL");

                    b.HasIndex("VehicleFinanceAgreementId")
                        .IsUnique()
                        .HasFilter("[VehicleFinanceAgreementId] IS NOT NULL");

                    b.HasIndex("VehicleId")
                        .IsUnique()
                        .HasFilter("[VehicleId] IS NOT NULL");

                    b.HasIndex("VehicleInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[VehicleInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("VehicleMotId")
                        .IsUnique()
                        .HasFilter("[VehicleMotId] IS NOT NULL");

                    b.HasIndex("VehicleServicePlanId")
                        .IsUnique()
                        .HasFilter("[VehicleServicePlanId] IS NOT NULL");

                    b.HasIndex("VehicleTaxId")
                        .IsUnique()
                        .HasFilter("[VehicleTaxId] IS NOT NULL");

                    b.HasIndex("VehicleWarrantyId")
                        .IsUnique()
                        .HasFilter("[VehicleWarrantyId] IS NOT NULL");

                    b.ToTable("Reminders");
                });

            modelBuilder.Entity("Life.Models.SavingsAccount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("AccountBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AccountType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("DateOpened")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("FixedTermEndDate")
                        .HasColumnType("date");

                    b.Property<int?>("FixedTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("InterestFrequency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccounts");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountAprHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<int>("SavingsAccountId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("SavingsAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccountAprHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountBalanceHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("AccountBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<int>("SavingsAccountId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("SavingsAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccountBalanceHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("DateOpened")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("FixedTermEndDate")
                        .HasColumnType("date");

                    b.Property<int?>("FixedTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("InterestFrequency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal>("PotBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PotType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SavingsAccountId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("SavingsAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccountPots");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPotAprHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<int>("SavingsAccountPotId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("SavingsAccountPotId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccountPotAprHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPotBalanceHistoryRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<decimal>("PotBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SavingsAccountPotId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("SavingsAccountPotId");

                    b.HasIndex("UserId");

                    b.ToTable("SavingsAccountPotBalanceHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.StandingOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CurrentAccountId")
                        .HasColumnType("int");

                    b.Property<int?>("GadgetInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("GeneralItemId")
                        .HasColumnType("int");

                    b.Property<int?>("LifeInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("LoanId")
                        .HasColumnType("int");

                    b.Property<int?>("MortgageId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("PaymentFrequency")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PaymentMonth")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("TenancyId")
                        .HasColumnType("int");

                    b.Property<int?>("TravelInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int?>("UtilityBillId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleFinanceAgreementId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleInsurancePolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("VehicleTaxId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddressInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[AddressInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("CurrentAccountId");

                    b.HasIndex("GadgetInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[GadgetInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("GeneralItemId")
                        .IsUnique()
                        .HasFilter("[GeneralItemId] IS NOT NULL");

                    b.HasIndex("LifeInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[LifeInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("LoanId")
                        .IsUnique()
                        .HasFilter("[LoanId] IS NOT NULL");

                    b.HasIndex("MortgageId")
                        .IsUnique()
                        .HasFilter("[MortgageId] IS NOT NULL");

                    b.HasIndex("TenancyId")
                        .IsUnique()
                        .HasFilter("[TenancyId] IS NOT NULL");

                    b.HasIndex("TravelInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[TravelInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.HasIndex("UtilityBillId")
                        .IsUnique()
                        .HasFilter("[UtilityBillId] IS NOT NULL");

                    b.HasIndex("VehicleFinanceAgreementId")
                        .IsUnique()
                        .HasFilter("[VehicleFinanceAgreementId] IS NOT NULL");

                    b.HasIndex("VehicleInsurancePolicyId")
                        .IsUnique()
                        .HasFilter("[VehicleInsurancePolicyId] IS NOT NULL");

                    b.HasIndex("VehicleTaxId")
                        .IsUnique()
                        .HasFilter("[VehicleTaxId] IS NOT NULL");

                    b.ToTable("StandingOrders");
                });

            modelBuilder.Entity("Life.Models.TaxableInterestPayment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CurrentAccountId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<decimal>("InterestAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SavingsAccountId")
                        .HasColumnType("int");

                    b.Property<int?>("SavingsAccountPotId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("CurrentAccountId");

                    b.HasIndex("SavingsAccountId");

                    b.HasIndex("SavingsAccountPotId");

                    b.HasIndex("UserId");

                    b.ToTable("TaxableInterestPayments");
                });

            modelBuilder.Entity("Life.Models.Tenancy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("AgentName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("AgentPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Deposit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("EndDate")
                        .HasColumnType("date");

                    b.Property<string>("LandlordName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LandlordPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("RentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Tenancies");
                });

            modelBuilder.Entity("Life.Models.TravelInsurancePolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRenewal")
                        .HasColumnType("bit");

                    b.Property<string>("ClaimsPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PolicyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PolicyNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("TravelInsurancePolicies");
                });

            modelBuilder.Entity("Life.Models.User", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Life.Models.UtilityBill", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<DateOnly?>("ContractEndDate")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("ContractStartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("ElectricityPencePerDay")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ElectricityPencePerKWh")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("GasPencePerDay")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("GasPencePerKWh")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("PaymentFrequency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PaymentMonth")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("TariffEndDate")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("TariffStartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("UtilityBills");
                });

            modelBuilder.Entity("Life.Models.Vehicle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("Colour")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("DatePurchased")
                        .HasColumnType("date");

                    b.Property<decimal?>("HighLoadFrontPressure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("HighLoadRearPressure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("KerbWeight")
                        .HasColumnType("int");

                    b.Property<decimal?>("LowLoadFrontPressure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LowLoadRearPressure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Make")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("NextServiceDue")
                        .HasColumnType("date");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Owner")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("OwnershipType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Registration")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("TyreSize")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("UnitOfPressure")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("VinNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Year")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("Life.Models.VehicleBreakdownPolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRenewal")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EUPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateOnly?>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NamedDriver")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("PolicyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PolicyNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<string>("UKPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int?>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleBreakdownPolicies");
                });

            modelBuilder.Entity("Life.Models.VehicleFinanceAgreement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("AgreementNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("Apr")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Deposit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly?>("ExpectedEndDate")
                        .HasColumnType("date");

                    b.Property<string>("FinanceType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("LoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LoanTermMonths")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentDayOfMonth")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("RemainingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("Settled")
                        .HasColumnType("bit");

                    b.Property<DateOnly?>("SettledDate")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("TotalRepayableAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.Property<decimal?>("VehiclePrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleFinanceAgreements");
                });

            modelBuilder.Entity("Life.Models.VehicleInsuranceClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateOnly>("ClaimDate")
                        .HasColumnType("date");

                    b.Property<string>("ClaimDetails")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ClaimReference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleInsurancePolicyId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleInsurancePolicyId");

                    b.ToTable("VehicleInsuranceClaims");
                });

            modelBuilder.Entity("Life.Models.VehicleInsurancePolicy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<int?>("AnnualMileage")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRenewal")
                        .HasColumnType("bit");

                    b.Property<string>("ClaimsPhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Excess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("GlassRepairExcess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GlassReplacementExcess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MainDriver")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NamedDrivers")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("PolicyCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PolicyNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int?>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleInsurancePolicies");
                });

            modelBuilder.Entity("Life.Models.VehicleMot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Advisories")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<int?>("Mileage")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TestCentre")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("TestDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleMots");
                });

            modelBuilder.Entity("Life.Models.VehicleService", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Mileage")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ServiceCentre")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("ServiceDate")
                        .HasColumnType("date");

                    b.Property<string>("ServiceType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleServices");
                });

            modelBuilder.Entity("Life.Models.VehicleServicePlan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PlanNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleServicePlans");
                });

            modelBuilder.Entity("Life.Models.VehicleTax", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleTaxes");
                });

            modelBuilder.Entity("Life.Models.VehicleWarranty", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PlanNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<int>("VehicleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("VehicleId");

                    b.ToTable("VehicleWarranties");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(225)
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(225)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(225)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(112)
                        .HasColumnType("nvarchar(112)");

                    b.Property<string>("Name")
                        .HasMaxLength(112)
                        .HasColumnType("nvarchar(112)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Life.Models.Address", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Addresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.AddressInsurancePolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("AddressInsurancePolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("AddressInsurancePolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.CreditCard", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("CreditCards")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("CreditCards")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.CreditCardAprHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.CreditCard", "CreditCard")
                        .WithMany("CreditCardAprHistoryRecords")
                        .HasForeignKey("CreditCardId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("CreditCardAprHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreditCard");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.CurrentAccount", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("CurrentAccounts")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("CurrentAccounts")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.CurrentAccountAprHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.CurrentAccount", "CurrentAccount")
                        .WithMany("CurrentAccountAprHistoryRecords")
                        .HasForeignKey("CurrentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("CurrentAccountAprHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CurrentAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.DebitCard", b =>
                {
                    b.HasOne("Life.Models.CurrentAccount", "CurrentAccount")
                        .WithOne("DebitCard")
                        .HasForeignKey("Life.Models.DebitCard", "CurrentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("DebitCards")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CurrentAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Dentist", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Dentists")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Dentists")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.DirectDebit", b =>
                {
                    b.HasOne("Life.Models.AddressInsurancePolicy", "AddressInsurancePolicy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "AddressInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.CurrentAccount", "CurrentAccount")
                        .WithMany("DirectDebits")
                        .HasForeignKey("CurrentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.GadgetInsurancePolicy", "GadgetInsurancePolicy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "GadgetInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.GeneralItem", "GeneralItem")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "GeneralItemId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.LifeInsurancePolicy", "LifeInsurancePolicy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "LifeInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Loan", "Loan")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "LoanId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Mortgage", "Mortgage")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "MortgageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Tenancy", "Tenancy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "TenancyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.TravelInsurancePolicy", "TravelInsurancePolicy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "TravelInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("DirectDebits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.UtilityBill", "UtilityBill")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "UtilityBillId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleFinanceAgreement", "VehicleFinanceAgreement")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "VehicleFinanceAgreementId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleInsurancePolicy", "VehicleInsurancePolicy")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "VehicleInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleTax", "VehicleTax")
                        .WithOne("DirectDebit")
                        .HasForeignKey("Life.Models.DirectDebit", "VehicleTaxId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AddressInsurancePolicy");

                    b.Navigation("CurrentAccount");

                    b.Navigation("GadgetInsurancePolicy");

                    b.Navigation("GeneralItem");

                    b.Navigation("LifeInsurancePolicy");

                    b.Navigation("Loan");

                    b.Navigation("Mortgage");

                    b.Navigation("Tenancy");

                    b.Navigation("TravelInsurancePolicy");

                    b.Navigation("User");

                    b.Navigation("UtilityBill");

                    b.Navigation("VehicleFinanceAgreement");

                    b.Navigation("VehicleInsurancePolicy");

                    b.Navigation("VehicleTax");
                });

            modelBuilder.Entity("Life.Models.DrivingLicence", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("DrivingLicences")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("DrivingLicences")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.EyeTest", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("EyeTests")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.GadgetInsurancePolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("GadgetInsurancePolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("GadgetInsurancePolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.GeneralItem", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("GeneralItems")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("GeneralItems")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.GlobalHealthInsuranceCard", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("GlobalHealthInsuranceCards")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.GpPractice", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("GpPractices")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("GpPractices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.LifeInsurancePolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("LifeInsurancePolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("LifeInsurancePolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Loan", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Loans")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Loans")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Mortgage", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Mortgages")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Mortgages")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Passport", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Passports")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Pension", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Pensions")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Pensions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.PensionBalanceHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.Pension", "Pension")
                        .WithMany("PensionBalanceHistoryRecords")
                        .HasForeignKey("PensionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("PensionBalanceHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Pension");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.RecurringPayment", b =>
                {
                    b.HasOne("Life.Models.CreditCard", "CreditCard")
                        .WithMany("RecurringPayments")
                        .HasForeignKey("CreditCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.DebitCard", "DebitCard")
                        .WithMany("RecurringPayments")
                        .HasForeignKey("DebitCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("RecurringPayments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreditCard");

                    b.Navigation("DebitCard");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Reminder", b =>
                {
                    b.HasOne("Life.Models.AddressInsurancePolicy", "AddressInsurancePolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "AddressInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.CreditCard", "CreditCard")
                        .WithMany("Reminders")
                        .HasForeignKey("CreditCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.DebitCard", "DebitCard")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "DebitCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.DrivingLicence", "DrivingLicence")
                        .WithMany("Reminders")
                        .HasForeignKey("DrivingLicenceId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.EyeTest", "EyeTest")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "EyeTestId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.GadgetInsurancePolicy", "GadgetInsurancePolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "GadgetInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.GeneralItem", "GeneralItem")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "GeneralItemId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.GlobalHealthInsuranceCard", "GlobalHealthInsuranceCard")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "GlobalHealthInsuranceCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.LifeInsurancePolicy", "LifeInsurancePolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "LifeInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Loan", "Loan")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "LoanId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Mortgage", "Mortgage")
                        .WithMany("Reminders")
                        .HasForeignKey("MortgageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Passport", "Passport")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "PassportId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.RecurringPayment", "RecurringPayment")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "RecurringPaymentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.SavingsAccount", "SavingsAccount")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "SavingsAccountId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.SavingsAccountPot", "SavingsAccountPot")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "SavingsAccountPotId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Tenancy", "Tenancy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "TenancyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.TravelInsurancePolicy", "TravelInsurancePolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "TravelInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Reminders")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.UtilityBill", "UtilityBill")
                        .WithMany("Reminders")
                        .HasForeignKey("UtilityBillId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleBreakdownPolicy", "VehicleBreakdownPolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleBreakdownPolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleFinanceAgreement", "VehicleFinanceAgreement")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleFinanceAgreementId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleInsurancePolicy", "VehicleInsurancePolicy")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleMot", "VehicleMot")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleMotId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleServicePlan", "VehicleServicePlan")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleServicePlanId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleTax", "VehicleTax")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleTaxId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleWarranty", "VehicleWarranty")
                        .WithOne("Reminder")
                        .HasForeignKey("Life.Models.Reminder", "VehicleWarrantyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AddressInsurancePolicy");

                    b.Navigation("CreditCard");

                    b.Navigation("DebitCard");

                    b.Navigation("DrivingLicence");

                    b.Navigation("EyeTest");

                    b.Navigation("GadgetInsurancePolicy");

                    b.Navigation("GeneralItem");

                    b.Navigation("GlobalHealthInsuranceCard");

                    b.Navigation("LifeInsurancePolicy");

                    b.Navigation("Loan");

                    b.Navigation("Mortgage");

                    b.Navigation("Passport");

                    b.Navigation("RecurringPayment");

                    b.Navigation("SavingsAccount");

                    b.Navigation("SavingsAccountPot");

                    b.Navigation("Tenancy");

                    b.Navigation("TravelInsurancePolicy");

                    b.Navigation("User");

                    b.Navigation("UtilityBill");

                    b.Navigation("Vehicle");

                    b.Navigation("VehicleBreakdownPolicy");

                    b.Navigation("VehicleFinanceAgreement");

                    b.Navigation("VehicleInsurancePolicy");

                    b.Navigation("VehicleMot");

                    b.Navigation("VehicleServicePlan");

                    b.Navigation("VehicleTax");

                    b.Navigation("VehicleWarranty");
                });

            modelBuilder.Entity("Life.Models.SavingsAccount", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("SavingsAccounts")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccounts")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountAprHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.SavingsAccount", "SavingsAccount")
                        .WithMany("SavingsAccountAprHistoryRecords")
                        .HasForeignKey("SavingsAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccountAprHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SavingsAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountBalanceHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.SavingsAccount", "SavingsAccount")
                        .WithMany("SavingsAccountBalanceHistoryRecords")
                        .HasForeignKey("SavingsAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccountBalanceHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SavingsAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPot", b =>
                {
                    b.HasOne("Life.Models.SavingsAccount", "SavingsAccount")
                        .WithMany("SavingsAccountPots")
                        .HasForeignKey("SavingsAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccountPots")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SavingsAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPotAprHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.SavingsAccountPot", "SavingsAccountPot")
                        .WithMany("SavingsAccountPotAprHistoryRecords")
                        .HasForeignKey("SavingsAccountPotId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccountPotAprHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SavingsAccountPot");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPotBalanceHistoryRecord", b =>
                {
                    b.HasOne("Life.Models.SavingsAccountPot", "SavingsAccountPot")
                        .WithMany("SavingsAccountPotBalanceHistoryRecords")
                        .HasForeignKey("SavingsAccountPotId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("SavingsAccountPotBalanceHistoryRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SavingsAccountPot");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.StandingOrder", b =>
                {
                    b.HasOne("Life.Models.AddressInsurancePolicy", "AddressInsurancePolicy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "AddressInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.CurrentAccount", "CurrentAccount")
                        .WithMany("StandingOrders")
                        .HasForeignKey("CurrentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.GadgetInsurancePolicy", "GadgetInsurancePolicy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "GadgetInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.GeneralItem", "GeneralItem")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "GeneralItemId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.LifeInsurancePolicy", "LifeInsurancePolicy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "LifeInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Loan", "Loan")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "LoanId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Mortgage", "Mortgage")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "MortgageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.Tenancy", "Tenancy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "TenancyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.TravelInsurancePolicy", "TravelInsurancePolicy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "TravelInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("StandingOrders")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.UtilityBill", "UtilityBill")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "UtilityBillId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleFinanceAgreement", "VehicleFinanceAgreement")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "VehicleFinanceAgreementId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleInsurancePolicy", "VehicleInsurancePolicy")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "VehicleInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.VehicleTax", "VehicleTax")
                        .WithOne("StandingOrder")
                        .HasForeignKey("Life.Models.StandingOrder", "VehicleTaxId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AddressInsurancePolicy");

                    b.Navigation("CurrentAccount");

                    b.Navigation("GadgetInsurancePolicy");

                    b.Navigation("GeneralItem");

                    b.Navigation("LifeInsurancePolicy");

                    b.Navigation("Loan");

                    b.Navigation("Mortgage");

                    b.Navigation("Tenancy");

                    b.Navigation("TravelInsurancePolicy");

                    b.Navigation("User");

                    b.Navigation("UtilityBill");

                    b.Navigation("VehicleFinanceAgreement");

                    b.Navigation("VehicleInsurancePolicy");

                    b.Navigation("VehicleTax");
                });

            modelBuilder.Entity("Life.Models.TaxableInterestPayment", b =>
                {
                    b.HasOne("Life.Models.CurrentAccount", "CurrentAccount")
                        .WithMany("TaxableInterestPayments")
                        .HasForeignKey("CurrentAccountId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.SavingsAccount", "SavingsAccount")
                        .WithMany("TaxableInterestPayments")
                        .HasForeignKey("SavingsAccountId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.SavingsAccountPot", "SavingsAccountPot")
                        .WithMany("TaxableInterestPayments")
                        .HasForeignKey("SavingsAccountPotId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("TaxableInterestPayments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CurrentAccount");

                    b.Navigation("SavingsAccount");

                    b.Navigation("SavingsAccountPot");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Tenancy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Tenancies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Tenancies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.TravelInsurancePolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("TravelInsurancePolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("TravelInsurancePolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.UtilityBill", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("UtilityBills")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("UtilityBills")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.Vehicle", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("Vehicles")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("Vehicles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Life.Models.VehicleBreakdownPolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("VehicleBreakdownPolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleBreakdownPolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleBreakdownPolicies")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Address");

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleFinanceAgreement", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("VehicleFinanceAgreements")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleFinanceAgreements")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleFinanceAgreements")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleInsuranceClaim", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleInsuranceClaims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.VehicleInsurancePolicy", "VehicleInsurancePolicy")
                        .WithMany("VehicleInsuranceClaims")
                        .HasForeignKey("VehicleInsurancePolicyId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("VehicleInsurancePolicy");
                });

            modelBuilder.Entity("Life.Models.VehicleInsurancePolicy", b =>
                {
                    b.HasOne("Life.Models.Address", "Address")
                        .WithMany("VehicleInsurancePolicies")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleInsurancePolicies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleInsurancePolicies")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Address");

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleMot", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleMots")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleMots")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleService", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleServices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleServices")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleServicePlan", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleServicePlans")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleServicePlans")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleTax", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleTaxes")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleTaxes")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Life.Models.VehicleWarranty", b =>
                {
                    b.HasOne("Life.Models.User", "User")
                        .WithMany("VehicleWarranties")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.Vehicle", "Vehicle")
                        .WithMany("VehicleWarranties")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Life.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Life.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Life.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Life.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Life.Models.Address", b =>
                {
                    b.Navigation("AddressInsurancePolicies");

                    b.Navigation("CreditCards");

                    b.Navigation("CurrentAccounts");

                    b.Navigation("Dentists");

                    b.Navigation("DrivingLicences");

                    b.Navigation("GadgetInsurancePolicies");

                    b.Navigation("GeneralItems");

                    b.Navigation("GpPractices");

                    b.Navigation("LifeInsurancePolicies");

                    b.Navigation("Loans");

                    b.Navigation("Mortgages");

                    b.Navigation("Pensions");

                    b.Navigation("SavingsAccounts");

                    b.Navigation("Tenancies");

                    b.Navigation("TravelInsurancePolicies");

                    b.Navigation("UtilityBills");

                    b.Navigation("VehicleBreakdownPolicies");

                    b.Navigation("VehicleFinanceAgreements");

                    b.Navigation("VehicleInsurancePolicies");

                    b.Navigation("Vehicles");
                });

            modelBuilder.Entity("Life.Models.AddressInsurancePolicy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.CreditCard", b =>
                {
                    b.Navigation("CreditCardAprHistoryRecords");

                    b.Navigation("RecurringPayments");

                    b.Navigation("Reminders");
                });

            modelBuilder.Entity("Life.Models.CurrentAccount", b =>
                {
                    b.Navigation("CurrentAccountAprHistoryRecords");

                    b.Navigation("DebitCard");

                    b.Navigation("DirectDebits");

                    b.Navigation("StandingOrders");

                    b.Navigation("TaxableInterestPayments");
                });

            modelBuilder.Entity("Life.Models.DebitCard", b =>
                {
                    b.Navigation("RecurringPayments");

                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.DrivingLicence", b =>
                {
                    b.Navigation("Reminders");
                });

            modelBuilder.Entity("Life.Models.EyeTest", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.GadgetInsurancePolicy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.GeneralItem", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.GlobalHealthInsuranceCard", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.LifeInsurancePolicy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.Loan", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.Mortgage", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminders");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.Passport", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.Pension", b =>
                {
                    b.Navigation("PensionBalanceHistoryRecords");
                });

            modelBuilder.Entity("Life.Models.RecurringPayment", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.SavingsAccount", b =>
                {
                    b.Navigation("Reminder");

                    b.Navigation("SavingsAccountAprHistoryRecords");

                    b.Navigation("SavingsAccountBalanceHistoryRecords");

                    b.Navigation("SavingsAccountPots");

                    b.Navigation("TaxableInterestPayments");
                });

            modelBuilder.Entity("Life.Models.SavingsAccountPot", b =>
                {
                    b.Navigation("Reminder");

                    b.Navigation("SavingsAccountPotAprHistoryRecords");

                    b.Navigation("SavingsAccountPotBalanceHistoryRecords");

                    b.Navigation("TaxableInterestPayments");
                });

            modelBuilder.Entity("Life.Models.Tenancy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.TravelInsurancePolicy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.User", b =>
                {
                    b.Navigation("AddressInsurancePolicies");

                    b.Navigation("Addresses");

                    b.Navigation("CreditCardAprHistoryRecords");

                    b.Navigation("CreditCards");

                    b.Navigation("CurrentAccountAprHistoryRecords");

                    b.Navigation("CurrentAccounts");

                    b.Navigation("DebitCards");

                    b.Navigation("Dentists");

                    b.Navigation("DirectDebits");

                    b.Navigation("DrivingLicences");

                    b.Navigation("EyeTests");

                    b.Navigation("GadgetInsurancePolicies");

                    b.Navigation("GeneralItems");

                    b.Navigation("GlobalHealthInsuranceCards");

                    b.Navigation("GpPractices");

                    b.Navigation("LifeInsurancePolicies");

                    b.Navigation("Loans");

                    b.Navigation("Mortgages");

                    b.Navigation("Passports");

                    b.Navigation("PensionBalanceHistoryRecords");

                    b.Navigation("Pensions");

                    b.Navigation("RecurringPayments");

                    b.Navigation("Reminders");

                    b.Navigation("SavingsAccountAprHistoryRecords");

                    b.Navigation("SavingsAccountBalanceHistoryRecords");

                    b.Navigation("SavingsAccountPotAprHistoryRecords");

                    b.Navigation("SavingsAccountPotBalanceHistoryRecords");

                    b.Navigation("SavingsAccountPots");

                    b.Navigation("SavingsAccounts");

                    b.Navigation("StandingOrders");

                    b.Navigation("TaxableInterestPayments");

                    b.Navigation("Tenancies");

                    b.Navigation("TravelInsurancePolicies");

                    b.Navigation("UtilityBills");

                    b.Navigation("VehicleBreakdownPolicies");

                    b.Navigation("VehicleFinanceAgreements");

                    b.Navigation("VehicleInsuranceClaims");

                    b.Navigation("VehicleInsurancePolicies");

                    b.Navigation("VehicleMots");

                    b.Navigation("VehicleServicePlans");

                    b.Navigation("VehicleServices");

                    b.Navigation("VehicleTaxes");

                    b.Navigation("VehicleWarranties");

                    b.Navigation("Vehicles");
                });

            modelBuilder.Entity("Life.Models.UtilityBill", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminders");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.Vehicle", b =>
                {
                    b.Navigation("Reminder");

                    b.Navigation("VehicleBreakdownPolicies");

                    b.Navigation("VehicleFinanceAgreements");

                    b.Navigation("VehicleInsurancePolicies");

                    b.Navigation("VehicleMots");

                    b.Navigation("VehicleServicePlans");

                    b.Navigation("VehicleServices");

                    b.Navigation("VehicleTaxes");

                    b.Navigation("VehicleWarranties");
                });

            modelBuilder.Entity("Life.Models.VehicleBreakdownPolicy", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.VehicleFinanceAgreement", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.VehicleInsurancePolicy", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");

                    b.Navigation("VehicleInsuranceClaims");
                });

            modelBuilder.Entity("Life.Models.VehicleMot", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.VehicleServicePlan", b =>
                {
                    b.Navigation("Reminder");
                });

            modelBuilder.Entity("Life.Models.VehicleTax", b =>
                {
                    b.Navigation("DirectDebit");

                    b.Navigation("Reminder");

                    b.Navigation("StandingOrder");
                });

            modelBuilder.Entity("Life.Models.VehicleWarranty", b =>
                {
                    b.Navigation("Reminder");
                });
#pragma warning restore 612, 618
        }
    }
}
