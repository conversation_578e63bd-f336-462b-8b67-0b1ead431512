@page
@model Life.Pages.Reminders.EditModel
@{
    ViewData["Title"] = "Edit Reminder";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Edit Reminder</h4>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    </div>
                }
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">
						<input type="hidden" asp-for="ReminderId" />
                        <div class="row my-3">
                            <label asp-for="Name" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="ReminderDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="ReminderDate" class="form-control" type="date" />
                                <span asp-validation-for="ReminderDate" class="text-danger"></span>
                            </div>
                        </div>



                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-primary">Save</button>
                                <a asp-page="./Index" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
