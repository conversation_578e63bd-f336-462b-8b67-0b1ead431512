@page
@model Life.Pages.Reminders.IndexModel
@{
    ViewData["Title"] = "Reminders";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Reminders</h4>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success add-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.Reminders.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Upcoming Reminders</h5>

                        <div class="list-group">
                            @foreach (var reminder in Model.Reminders)
                            {
                                if (reminder.IsAutomatic)
                                {
                                    //to do
                                }
                                else
                                {
                                    <div class="list-group-item py-3 reminder">
                                        <div class="d-flex justify-content-between my-3">
                                            <div>
                                                <h5 class="mb-2 text-break">@reminder.Name</h5>
                                                <h6 class="text-muted">@reminder.ReminderDate</h6>
                                                <a asp-page="./Edit" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                            <a asp-page="./Delete" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                }
                            }
                            </div>
                    </div>
                } 
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Upcoming Reminders</h5>
                        <p>There are no upcoming reminders.</p>
                    </div>
                }



                @if (Model.ExpiredReminders.Any())
                {
                    <hr class="mt-5" />
                    <div class="card-body expired-reminders">
                        <h5 class="card-title">Expired Reminders</h5>

                        <div class="list-group">
                            @foreach (var reminder in Model.ExpiredReminders)
                            {
                                if (reminder.IsAutomatic)
                                {
                                    //to do
                                }
                                else
                                {
                                    <div class="list-group-item py-3 reminder">
                                        <div class="d-flex justify-content-between my-3">
                                            <div>
                                                <h5 class="mb-2 text-break">@reminder.Name</h5>
                                                <h6 class="text-muted">@reminder.ReminderDate</h6>
                                                <a asp-page="./Edit" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                            <a asp-page="./Delete" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                }
                            }
                            </div>
                    </div>
                }

            </div>
        </div>
    </div>
</div>


